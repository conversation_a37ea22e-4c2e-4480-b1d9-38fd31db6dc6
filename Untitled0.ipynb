{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cuQA96FcoraD", "outputId": "fcd2766a-a565-4385-f4db-9bffc1ea71f4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2025-06-05 08:54:44,541 - INFO - 训练配置: {'batch_size': 16, 'epochs': 20, 'lr': 0.0005, 'd_lr': 0.0001, 'weight_decay': 1e-06, 'scheduler': 'plateau', 'lr_patience': 3, 'lr_factor': 0.5, 'min_lr': 1e-07, 'MS_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS', 'NTL_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL', 'save_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/model', 'up_scale': 4, 'cuda': True, 'lambda_adv': 0.5, 'lambda_cycle': 0.1, 'lambda_identity': 0.3, 'save_interval': 10, 'val_ratio': 0.1, 'seed': 42, 'n_critic': 2, 'warmup_epochs': 0}\n", "2025-06-05 08:54:44,541 - INFO - 使用设备: cuda\n", "2025-06-05 08:54:44,988 - INFO - G_LR_to_HR参数数量: 1039539\n", "2025-06-05 08:54:44,989 - INFO - G_HR_to_LR参数数量: 744276\n", "2025-06-05 08:54:44,989 - INFO - D_HR参数数量: 1555329\n", "2025-06-05 08:54:44,989 - INFO - D_LR参数数量: 1555329\n", "/usr/local/lib/python3.11/dist-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n", "2025-06-05 08:54:44,991 - INFO - 使用ReduceLROnPlateau学习率调度器 (因子: 0.5, 耐心值: 3)\n", "No blur will be applied to NTL HR before downsampling.\n", "2025-06-05 08:54:53,113 - INFO - 数据集总大小: 1600\n", "2025-06-05 08:54:53,114 - INFO - 训练集大小: 1440\n", "2025-06-05 08:54:53,114 - INFO - 验证集大小: 160\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/data/dataloader.py:624: UserWarning: This DataLoader will create 4 worker processes in total. Our suggested max number of worker in current system is 2, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.\n", "  warnings.warn(\n", "2025-06-05 08:59:31,964 - INFO - Epoch [1/20], G训练: 0.8364, G验证: 0.5134, D_HR训练: 0.7771, D_HR验证: 0.6862, D_LR训练: 0.7161, D_LR验证: 0.8176, 循环损失: 0.0004,  PSNR: 53.02dB, SSIM: 0.9744,mixed_score：0.9885 学习率G: 5.00e-04, 学习率D_HR: 1.00e-04, 学习率D_LR: 1.00e-04, 时间: 0:04:38.846776\n", "2025-06-05 08:59:32,287 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-05 09:01:22,293 - INFO - Epoch [2/20], G训练: 0.7147, G验证: 0.7981, D_HR训练: 0.7019, D_HR验证: 0.6918, D_LR训练: 0.7074, D_LR验证: 0.7188, 循环损失: 0.0003,  PSNR: 55.24dB, SSIM: 0.9863,mixed_score：0.9938 学习率G: 5.00e-04, 学习率D_HR: 1.00e-04, 学习率D_LR: 1.00e-04, 时间: 0:01:50.002389\n", "2025-06-05 09:01:22,635 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-05 09:03:08,115 - INFO - Epoch [3/20], G训练: 0.6979, G验证: 0.6965, D_HR训练: 0.6954, D_HR验证: 0.6898, D_LR训练: 0.6991, D_LR验证: 0.6846, 循环损失: 0.0006,  PSNR: 53.53dB, SSIM: 0.9783,mixed_score：0.9902 学习率G: 5.00e-04, 学习率D_HR: 1.00e-04, 学习率D_LR: 1.00e-04, 时间: 0:01:45.476291\n", "2025-06-05 09:04:51,596 - INFO - Epoch [4/20], G训练: 0.7084, G验证: 0.6806, D_HR训练: 0.6960, D_HR验证: 0.6889, D_LR训练: 0.6962, D_LR验证: 0.6936, 循环损失: 0.0008,  PSNR: 53.79dB, SSIM: 0.9797,mixed_score：0.9908 学习率G: 5.00e-04, 学习率D_HR: 1.00e-04, 学习率D_LR: 1.00e-04, 时间: 0:01:43.477628\n", "2025-06-05 09:06:37,940 - INFO - Epoch [5/20], G训练: 0.6901, G验证: 0.6873, D_HR训练: 0.6955, D_HR验证: 0.6895, D_LR训练: 0.6957, D_LR验证: 0.6929, 循环损失: 0.0007,  PSNR: 49.92dB, SSIM: 0.9461,mixed_score：0.9748 学习率G: 2.50e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 1.00e-04, 时间: 0:01:46.342697\n", "2025-06-05 09:08:14,796 - INFO - Epoch [6/20], G训练: 0.7179, G验证: 0.6895, D_HR训练: 0.6936, D_HR验证: 0.6850, D_LR训练: 0.6857, D_LR验证: 0.6882, 循环损失: 0.0004,  PSNR: 52.17dB, SSIM: 0.9703,mixed_score：0.9866 学习率G: 2.50e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 1.00e-04, 时间: 0:01:36.853151\n", "2025-06-05 09:09:53,466 - INFO - Epoch [7/20], G训练: 0.7144, G验证: 0.6780, D_HR训练: 0.7141, D_HR验证: 0.6964, D_LR训练: 0.6959, D_LR验证: 0.6914, 循环损失: 0.0007,  PSNR: 46.92dB, SSIM: 0.9246,mixed_score：0.9322 学习率G: 2.50e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 5.00e-05, 时间: 0:01:38.667620\n", "2025-06-05 09:11:39,075 - INFO - Epoch [8/20], G训练: 0.6946, G验证: 0.7058, D_HR训练: 0.6961, D_HR验证: 0.6665, D_LR训练: 0.6931, D_LR验证: 0.6961, 循环损失: 0.0015,  PSNR: 35.47dB, SSIM: 0.7519,mixed_score：0.7285 学习率G: 2.50e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 5.00e-05, 时间: 0:01:45.607181\n", "2025-06-05 09:13:26,431 - INFO - Epoch [9/20], G训练: 0.6956, G验证: 0.6965, D_HR训练: 0.6930, D_HR验证: 0.6927, D_LR训练: 0.6934, D_LR验证: 0.6941, 循环损失: 0.0005,  PSNR: 55.17dB, SSIM: 0.9935,mixed_score：0.9971 学习率G: 1.25e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 5.00e-05, 时间: 0:01:47.353376\n", "2025-06-05 09:13:26,676 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-05 09:15:15,574 - INFO - Epoch [10/20], G训练: 0.6975, G验证: 0.7190, D_HR训练: 0.6923, D_HR验证: 0.6945, D_LR训练: 0.6915, D_LR验证: 0.6905, 循环损失: 0.0002,  PSNR: 58.30dB, SSIM: 0.9939,mixed_score：0.9973 学习率G: 1.25e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 5.00e-05, 时间: 0:01:48.894755\n", "2025-06-05 09:15:15,836 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-05 09:17:03,089 - INFO - Epoch [11/20], G训练: 0.7016, G验证: 0.6786, D_HR训练: 0.6913, D_HR验证: 0.6875, D_LR训练: 0.6918, D_LR验证: 0.6936, 循环损失: 0.0005,  PSNR: 49.03dB, SSIM: 0.9713,mixed_score：0.9764 学习率G: 1.25e-04, 学习率D_HR: 5.00e-05, 学习率D_LR: 2.50e-05, 时间: 0:01:47.249937\n", "2025-06-05 09:18:39,688 - INFO - Epoch [12/20], G训练: 0.7042, G验证: 0.6455, D_HR训练: 0.6864, D_HR验证: 0.6810, D_LR训练: 0.6929, D_LR验证: 0.6939, 循环损失: 0.0003,  PSNR: 57.83dB, SSIM: 0.9945,mixed_score：0.9975 学习率G: 1.25e-04, 学习率D_HR: 2.50e-05, 学习率D_LR: 2.50e-05, 时间: 0:01:36.596228\n", "2025-06-05 09:18:39,940 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-05 09:20:19,138 - INFO - Epoch [13/20], G训练: 0.6867, G验证: 0.5667, D_HR训练: 0.7286, D_HR验证: 0.9336, D_LR训练: 0.6931, D_LR验证: 0.6924, 循环损失: 0.0045,  PSNR: 26.61dB, SSIM: 0.2932,mixed_score：0.4246 学习率G: 6.25e-05, 学习率D_HR: 2.50e-05, 学习率D_LR: 2.50e-05, 时间: 0:01:39.194032\n", "2025-06-05 09:22:02,400 - INFO - Epoch [14/20], G训练: 0.8437, G验证: 0.6849, D_HR训练: 0.5376, D_HR验证: 0.7510, D_LR训练: 0.6929, D_LR验证: 0.6931, 循环损失: 0.0003,  PSNR: 53.32dB, SSIM: 0.9919,mixed_score：0.9963 学习率G: 6.25e-05, 学习率D_HR: 2.50e-05, 学习率D_LR: 2.50e-05, 时间: 0:01:43.260029\n", "2025-06-05 09:23:44,473 - INFO - Epoch [15/20], G训练: 0.7406, G验证: 0.6960, D_HR训练: 0.7158, D_HR验证: 0.6898, D_LR训练: 0.6932, D_LR验证: 0.6928, 循环损失: 0.0004,  PSNR: 56.41dB, SSIM: 0.9923,mixed_score：0.9965 学习率G: 6.25e-05, 学习率D_HR: 2.50e-05, 学习率D_LR: 1.25e-05, 时间: 0:01:42.070617\n", "2025-06-05 09:25:26,746 - INFO - Epoch [16/20], G训练: 0.6970, G验证: 0.7229, D_HR训练: 0.6915, D_HR验证: 0.6958, D_LR训练: 0.6930, D_LR验证: 0.6928, 循环损失: 0.0003,  PSNR: 55.30dB, SSIM: 0.9931,mixed_score：0.9969 学习率G: 6.25e-05, 学习率D_HR: 1.25e-05, 学习率D_LR: 1.25e-05, 时间: 0:01:42.270010\n", "2025-06-05 09:27:11,635 - INFO - Epoch [17/20], G训练: 0.7001, G验证: 0.6977, D_HR训练: 0.6886, D_HR验证: 0.6935, D_LR训练: 0.6931, D_LR验证: 0.6929, 循环损失: 0.0003,  PSNR: 55.58dB, SSIM: 0.9926,mixed_score：0.9967 学习率G: 3.13e-05, 学习率D_HR: 1.25e-05, 学习率D_LR: 1.25e-05, 时间: 0:01:44.886500\n", "2025-06-05 09:28:48,690 - INFO - Epoch [18/20], G训练: 0.6997, G验证: 0.6916, D_HR训练: 0.6863, D_HR验证: 0.6840, D_LR训练: 0.6931, D_LR验证: 0.6928, 循环损失: 0.0003,  PSNR: 57.91dB, SSIM: 0.9930,mixed_score：0.9969 学习率G: 3.13e-05, 学习率D_HR: 1.25e-05, 学习率D_LR: 1.25e-05, 时间: 0:01:37.053910\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/train.py"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9892, "status": "ok", "timestamp": 1749110117966, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "3rPuSnZ6orQr", "outputId": "3a096e32-59aa-427a-ca3a-d55fde40a29b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Namespace(MS_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/MS', NPP_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/NPP', model='/content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth', save_folder='/content/drive/MyDrive/d2l-zh/SRGAN/data/out', cuda=True)\n", "使用设备: cuda\n", "成功加载 CycleGAN 模型的 G_LR_to_HR 生成器，并切换到 eval 模式\n", "找到 5 个多光谱图像\n", "找到 5 个NPP图像\n", "处理的图像对数量: 5\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_1.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_2.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_3.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_4.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_5.tif\n", "收皮\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/test.py"]}, {"cell_type": "code", "source": [], "metadata": {"id": "rlAcUai3wR6c"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3985, "status": "ok", "timestamp": 1749109517418, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "hVZEBNhworA8", "outputId": "1c3ef2af-c139-4ab9-a96f-08d39a37bebb"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting rasterio\n", "  Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.1 kB)\n", "Collecting affine (from rasterio)\n", "  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs in /usr/local/lib/python3.11/dist-packages (from rasterio) (25.3.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from rasterio) (2025.4.26)\n", "Requirement already satisfied: click>=4.0 in /usr/local/lib/python3.11/dist-packages (from rasterio) (8.2.1)\n", "Collecting cligj>=0.5 (from rasterio)\n", "  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: numpy>=1.24 in /usr/local/lib/python3.11/dist-packages (from rasterio) (2.0.2)\n", "Collecting click-plugins (from rasterio)\n", "  Downloading click_plugins-1.1.1-py2.py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: pyparsing in /usr/local/lib/python3.11/dist-packages (from rasterio) (3.2.3)\n", "Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.2/22.2 MB\u001b[0m \u001b[31m74.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading cligj-0.7.2-py3-none-any.whl (7.1 kB)\n", "Downloading affine-2.4.0-py3-none-any.whl (15 kB)\n", "Downloading click_plugins-1.1.1-py2.py3-none-any.whl (7.5 kB)\n", "Installing collected packages: cligj, click-plugins, affine, rasterio\n", "Successfully installed affine-2.4.0 click-plugins-1.1.1 cligj-0.7.2 rasterio-1.4.3\n"]}], "source": ["!pip install rasterio"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 85677, "status": "ok", "timestamp": 1749109603098, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "4lEir2SAPO24", "outputId": "800e3c1a-82ac-47c6-fc25-497befd0aca8"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pytorch_msssim\n", "  Downloading pytorch_msssim-1.0.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from pytorch_msssim) (2.6.0+cu124)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->pytorch_msssim) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->pytorch_msssim) (3.0.2)\n", "Downloading pytorch_msssim-1.0.0-py3-none-any.whl (7.7 kB)\n", "Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m3.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m43.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m26.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m37.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m13.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m76.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, pytorch_msssim\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 pytorch_msssim-1.0.0\n"]}], "source": ["!pip install pytorch_msssim"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3042, "status": "ok", "timestamp": 1749109797567, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "G2NzqWprZw59", "outputId": "4686c0c2-bdbb-48a8-d5a5-5cc50cf9bac4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "d-wbLVxxj2zv"}, "outputs": [], "source": ["import os\n", "\n", "try:\n", "    import gdal\n", "except:\n", "    from osgeo import gdal\n", "import numpy as np\n", "\n", "\n", "# 读取tif数据集\n", "def readTif(fileName):\n", "    dataset = gdal.Open(fileName)\n", "    if dataset is None:\n", "        print(fileName + \"文件无法打开\")\n", "    return dataset\n", "\n", "\n", "# 保存tif文件函数（改进数据类型处理和添加压缩方式）\n", "def writeTiff(im_data, im_geotrans, im_proj, path, datatype, compress='LZW'):\n", "    # 根据输入数据的维度判断波段数\n", "    if len(im_data.shape) == 3:\n", "        im_bands, im_height, im_width = im_data.shape\n", "    elif len(im_data.shape) == 2:\n", "        im_data = np.array([im_data])\n", "        im_bands, im_height, im_width = im_data.shape\n", "\n", "    # 创建文件，使用压缩选项（LZW, DEFLATE等）\n", "    driver = gdal.Get<PERSON><PERSON><PERSON><PERSON><PERSON>(\"GTiff\")\n", "    options = [\"COMPRESS={}\".format(compress)]\n", "    dataset = driver.Create(path, int(im_width), int(im_height), int(im_bands), datatype, options=options)\n", "\n", "    if dataset is not None:\n", "        dataset.SetGeoTransform(im_geotrans)  # 写入仿射变换参数\n", "        dataset.SetProjection(im_proj)  # 写入投影\n", "\n", "    # 写入每个波段\n", "    for i in range(im_bands):\n", "        dataset.GetRasterBand(i + 1).WriteArray(im_data[i])\n", "    del dataset\n", "\n", "\n", "# 像素坐标和地理坐标仿射变换\n", "def CoordTransf(Xpixel, Ypixel, GeoTransform):\n", "    XGeo = GeoTransform[0] + GeoTransform[1] * Xpixel + Ypixel * GeoTransform[2]\n", "    YGeo = GeoTransform[3] + GeoTransform[4] * Xpixel + Ypixel * GeoTransform[5]\n", "    return <PERSON><PERSON><PERSON>, YGeo\n", "\n", "\n", "# 裁剪函数，改进数据类型处理\n", "def TifCrop(Tif<PERSON>ath, SavePath, CropSize, RepetitionRate):\n", "    if not os.path.exists(SavePath):\n", "        os.makedirs(SavePath)\n", "\n", "    dataset_img = readTif(TifPath)\n", "    width = dataset_img.RasterXSize\n", "    height = dataset_img.RasterYSize\n", "    proj = dataset_img.GetProjection()\n", "    geotrans = dataset_img.GetGeoTransform()\n", "    img = dataset_img.ReadAsArray(0, 0, width, height)  # 获取数据\n", "\n", "    # 获取数据类型，保持与原始影像一致\n", "    datatype = dataset_img.GetRasterBand(1).DataType\n", "\n", "    new_name = len(os.listdir(SavePath)) + 1\n", "\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "            # 如果图像是单波段\n", "            if len(img.shape) == 2:\n", "                cropped = img[\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "            # 如果图像是多波段\n", "            else:\n", "                cropped = img[:,\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "            XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)),\n", "                                     int(i * CropSize * (1 - RepetitionRate)),\n", "                                     geotrans)\n", "            crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "            writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "            new_name += 1\n", "\n", "    # 向前裁剪最后一列\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "        else:\n", "            cropped = img[:,\n", "                      int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "\n", "        XGeo, YGeo = CoordTransf(width - CropSize, int(i * CropSize * (1 - RepetitionRate)), geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 向前裁剪最后一行\n", "    for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[(height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "        else:\n", "            cropped = img[:,\n", "                      (height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "        XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)), height - CropSize, geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 裁剪右下角\n", "    if len(img.shape) == 2:\n", "        cropped = img[(height - CropSize): height, (width - CropSize): width]\n", "    else:\n", "        cropped = img[:, (height - CropSize): height, (width - CropSize): width]\n", "\n", "    XGeo, YGeo = CoordTransf(width - CropSize, height - CropSize, geotrans)\n", "    crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "    writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "    new_name += 1\n", "\n", "\n", "# 将影像1裁剪为重复率为0.1的64×64的数据集\n", "TifCrop(r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/B0.tif\", r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL/\", 80, 0.5)\n"]}, {"cell_type": "code", "source": [], "metadata": {"id": "1tpzUV3eYIQx"}, "execution_count": null, "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}