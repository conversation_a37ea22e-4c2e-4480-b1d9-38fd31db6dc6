import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, random_split
from torch.optim.lr_scheduler import CosineAnnealingLR, ReduceLROnPlateau, StepLR
import os
import numpy as np
import matplotlib.pyplot as plt
from utils.loss import FusionLoss
from model import CycleGANNightLight  # 修改：导入完整的CycleGAN模型
from utils.dataset import MedicalImageDataset
import argparse
import logging
from datetime import datetime
from utils.log import TrainingVisualizer
from utils.vloss import ImageMetrics
import torch.nn.functional as F
import math
# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler('training.log'), logging.StreamHandler()]
)
# UCGAN风格的损失函数
class QNRLoss(nn.Module):
    """QNR损失 - 针对遥感影像的无参考质量评估"""
    def __init__(self, device):
        super(QNRLoss, self).__init__()
        self.device = device

    def forward(self, pan, ms, output):
        """
        Args:
            pan: 全色图像 (高分辨率单通道)
            ms: 多光谱图像 (低分辨率多通道)
            output: 融合输出 (高分辨率多通道)
        """
        # 计算D_lambda (光谱失真)
        ms_mean = torch.mean(ms, dim=[2, 3], keepdim=True)
        output_downsampled = F.interpolate(output, size=ms.shape[-2:], mode='area')
        output_mean = torch.mean(output_downsampled, dim=[2, 3], keepdim=True)

        # 光谱相关性
        ms_centered = ms - ms_mean
        output_centered = output_downsampled - output_mean

        numerator = torch.sum(ms_centered * output_centered, dim=[2, 3])
        ms_var = torch.sum(ms_centered ** 2, dim=[2, 3])
        output_var = torch.sum(output_centered ** 2, dim=[2, 3])

        correlation = numerator / (torch.sqrt(ms_var * output_var) + 1e-8)
        D_lambda = torch.mean(1 - torch.abs(correlation))

        # 计算D_s (空间失真)
        pan_downsampled = F.interpolate(pan, size=ms.shape[-2:], mode='area')
        output_pan = torch.mean(output, dim=1, keepdim=True)  # 转换为单通道

        pan_mean = torch.mean(pan_downsampled, dim=[2, 3], keepdim=True)
        output_pan_mean = torch.mean(output_pan, dim=[2, 3], keepdim=True)

        pan_centered = pan_downsampled - pan_mean
        output_pan_centered = output_pan - output_pan_mean

        numerator_s = torch.sum(pan_centered * output_pan_centered, dim=[2, 3])
        pan_var = torch.sum(pan_centered ** 2, dim=[2, 3])
        output_pan_var = torch.sum(output_pan_centered ** 2, dim=[2, 3])

        correlation_s = numerator_s / (torch.sqrt(pan_var * output_pan_var) + 1e-8)
        D_s = torch.mean(1 - torch.abs(correlation_s))

        # QNR = (1 - D_lambda) * (1 - D_s)
        QNR = (1 - D_lambda) * (1 - D_s)
        return 1 - QNR  # 返回损失值（越小越好）

class SpectralLoss(nn.Module):
    """光谱一致性损失"""
    def __init__(self):
        super(SpectralLoss, self).__init__()
        self.l1_loss = nn.L1Loss()

    def forward(self, output, target):
        # 低通滤波保持光谱信息
        output_lp = F.avg_pool2d(output, kernel_size=3, stride=1, padding=1)
        target_lp = F.avg_pool2d(target, kernel_size=3, stride=1, padding=1)
        return self.l1_loss(output_lp, target_lp)

class SpatialLoss(nn.Module):
    """空间细节损失"""
    def __init__(self):
        super(SpatialLoss, self).__init__()
        self.l1_loss = nn.L1Loss()

    def forward(self, output, target):
        # 高通滤波保持空间细节
        kernel = torch.tensor([[-1, -1, -1], [-1, 8, -1], [-1, -1, -1]],
                             dtype=torch.float32, device=output.device).unsqueeze(0).unsqueeze(0)
        kernel = kernel.repeat(output.size(1), 1, 1, 1)

        output_hp = F.conv2d(output, kernel, padding=1, groups=output.size(1))
        target_hp = F.conv2d(target, kernel, padding=1, groups=target.size(1))
        return self.l1_loss(output_hp, target_hp)

def compute_mixed_score(psnr, ssim, perceptual_score, adv_score, weights=None):
    """
    计算改进的混合评分 (针对夜光遥感影像优化)
    Args:
        psnr: PSNR值 (通常0-60 dB)
        ssim: SSIM值 (0-1)
        perceptual_score: 夜光影像感知质量分数 (0-1, 越大越好)
        adv_score: 对抗分数 (0-1, 越大越好)
        weights: 权重列表 [psnr_weight, ssim_weight, perceptual_weight, adv_weight]
    """
    if weights is None:
        weights = [0.35, 0.25, 0.25, 0.15]  # 针对夜光影像调整的权重

    # 归一化各指标到[0,1]范围
    normalized_psnr = min(psnr / 60.0, 1.0)  # PSNR范围0-60 dB
    normalized_ssim = ssim  # SSIM本身就是0-1
    normalized_perceptual = perceptual_score  # 感知分数本身就是0-1
    normalized_adv = adv_score  # 对抗分数本身就是0-1

    mixed_score = (weights[0] * normalized_psnr +
                   weights[1] * normalized_ssim +
                   weights[2] * normalized_perceptual +
                   weights[3] * normalized_adv)

    return mixed_score
# 训练参数设置
parser = argparse.ArgumentParser(description='CycleGAN夜光影像超分辨率训练脚本')
parser.add_argument('--batch_size', type=int, default=16, help='训练批次大小')  # 降低batch_size
parser.add_argument('--epochs', type=int, default=20, help='训练轮数')
parser.add_argument('--lr', type=float, default=5e-4 , help='初始学习率')
parser.add_argument('--d_lr', type=float, default=2e-5 , help='判别器学习率')
parser.add_argument('--weight_decay', type=float, default=1e-6, help='权重衰减参数')
parser.add_argument('--scheduler', type=str, default='plateau', choices=['cosine', 'plateau', 'none'], help='学习率调度器类型')
parser.add_argument('--lr_patience', type=int, default=3, help='ReduceLROnPlateau的patience参数')
parser.add_argument('--lr_factor', type=float, default=0.5, help='ReduceLROnPlateau的factor参数')
parser.add_argument('--min_lr', type=float, default=1e-7, help='最小学习率')
parser.add_argument('--MS_data_dir', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS', help='多光谱图像目录')
parser.add_argument('--NTL_data_dir', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL', help='夜间灯光图像目录')
parser.add_argument('--save_dir', type=str, default='/content/drive/MyDrive/d2l-zh/SRGAN/data/model', help='模型保存目录')
parser.add_argument('--up_scale', type=int, default=4, help='上采样倍数')
parser.add_argument('--cuda', action='store_true', help='使用CUDA', default=True)

# CycleGAN损失权重参数 - UCGAN风格优化
parser.add_argument('--lambda_rad', type=float, default=1, help='辐射一致性保持损失权重')
parser.add_argument('--lambda_perc', type=float, default=2, help='感知损失权重')
parser.add_argument('--lambda_struct', type=float, default=1, help='结构损失权重')
parser.add_argument('--lambda_adv', type=float, default=0.001, help='对抗损失权重(UCGAN风格)')
parser.add_argument('--lambda_cycle', type=float, default=0.001, help='循环一致性损失权重(UCGAN风格)')
parser.add_argument('--lambda_identity', type=float, default=0.5, help='身份损失权重')
parser.add_argument('--lambda_qnr', type=float, default=1.0, help='QNR损失权重(遥感专用)')
parser.add_argument('--lambda_spectral', type=float, default=0.0005, help='光谱损失权重')
parser.add_argument('--lambda_spatial', type=float, default=0.0005, help='空间损失权重')
parser.add_argument('--use_soft_labels', action='store_true', default=True, help='使用软标签(UCGAN风格)')
parser.add_argument('--use_step_scheduler', action='store_true', default=False, help='使用StepLR调度器')

parser.add_argument('--save_interval', type=int, default=10, help='模型保存间隔(epoch)')
parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集占总数据的比例')
parser.add_argument('--seed', type=int, default=42, help='随机种子，用于数据集划分')
parser.add_argument('--n_critic', type=int, default=2, help='每训练n_critic次判别器更新一次生成器')
parser.add_argument('--warmup_epochs', type=int, default=5, help='预热轮数，只训练生成器')

opt = parser.parse_args()
visualizer = TrainingVisualizer(save_dir=opt.save_dir)

def train():
    # 设置随机种子以确保可重复性
    torch.manual_seed(opt.seed)
    np.random.seed(opt.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(opt.seed)
    
    # 创建保存目录
    if not os.path.exists(opt.save_dir):
        os.makedirs(opt.save_dir)
    
    # 记录当前时间，用于模型命名
    current_time = datetime.now().strftime('%Y%m%d_%H%M')
    # 记录训练配置
    logging.info(f"训练配置: {vars(opt)}")

    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() and opt.cuda else 'cpu')
    logging.info(f"使用设备: {device}")

    # 初始化CycleGAN模型
    model = CycleGANNightLight(scale_factor=opt.up_scale)
    model = model.to(device)
    
    # 分别获取各个网络组件
    G_LR_to_HR = model.G_LR_to_HR
    G_HR_to_LR = model.G_HR_to_LR
    D_HR = model.D_HR
    D_LR = model.D_LR
    
    logging.info(f"G_LR_to_HR参数数量: {sum(p.numel() for p in G_LR_to_HR.parameters() if p.requires_grad)}")
    logging.info(f"G_HR_to_LR参数数量: {sum(p.numel() for p in G_HR_to_LR.parameters() if p.requires_grad)}")
    logging.info(f"D_HR参数数量: {sum(p.numel() for p in D_HR.parameters() if p.requires_grad)}")
    logging.info(f"D_LR参数数量: {sum(p.numel() for p in D_LR.parameters() if p.requires_grad)}")

    # 初始化优化器
    optimizer_G = optim.Adam(
        list(G_LR_to_HR.parameters()) + list(G_HR_to_LR.parameters()), 
        lr=opt.lr, 
        betas=(0.5, 0.999),  # CycleGAN推荐的beta参数
        weight_decay=opt.weight_decay
    )
    optimizer_D_HR = optim.Adam(D_HR.parameters(), lr=opt.d_lr, betas=(0.5, 0.999), weight_decay=opt.weight_decay)
    optimizer_D_LR = optim.Adam(D_LR.parameters(), lr=opt.d_lr, betas=(0.5, 0.999), weight_decay=opt.weight_decay)
    
    # 初始化学习率调度器 - UCGAN风格优化
    if opt.use_step_scheduler:
        # UCGAN风格的StepLR调度器
        step_size = max(opt.epochs // 2, 10)  # 在训练中期降低学习率
        scheduler_G = StepLR(optimizer_G, step_size=step_size, gamma=0.9)
        scheduler_D_HR = StepLR(optimizer_D_HR, step_size=step_size, gamma=0.9)
        scheduler_D_LR = StepLR(optimizer_D_LR, step_size=step_size, gamma=0.9)
        logging.info(f"使用StepLR学习率调度器 (步长: {step_size}, 衰减因子: 0.9)")
    elif opt.scheduler == 'cosine':
        scheduler_G = CosineAnnealingLR(optimizer_G, T_max=opt.epochs, eta_min=opt.min_lr)
        scheduler_D_HR = CosineAnnealingLR(optimizer_D_HR, T_max=opt.epochs, eta_min=opt.min_lr)
        scheduler_D_LR = CosineAnnealingLR(optimizer_D_LR, T_max=opt.epochs, eta_min=opt.min_lr)
        logging.info("使用余弦退火学习率调度器")
    elif opt.scheduler == 'plateau':
        scheduler_G = ReduceLROnPlateau(optimizer_G, mode='min', factor=opt.lr_factor,
                                     patience=opt.lr_patience, min_lr=opt.min_lr, verbose=True)
        scheduler_D_HR = ReduceLROnPlateau(optimizer_D_HR, mode='min', factor=opt.lr_factor,
                                        patience=opt.lr_patience, min_lr=opt.min_lr, verbose=True)
        scheduler_D_LR = ReduceLROnPlateau(optimizer_D_LR, mode='min', factor=opt.lr_factor,
                                        patience=opt.lr_patience, min_lr=opt.min_lr, verbose=True)
        logging.info(f"使用ReduceLROnPlateau学习率调度器 (因子: {opt.lr_factor}, 耐心值: {opt.lr_patience})")
    else:
        scheduler_G = None
        scheduler_D_HR = None
        scheduler_D_LR = None
        logging.info("不使用学习率调度器")

    # 初始化损失函数 - UCGAN风格优化
    criterion_content = FusionLoss(
        lambda_rad=opt.lambda_rad,
        lambda_perc=opt.lambda_perc,
        lambda_struct=opt.lambda_struct,
    ).to(device)

    # UCGAN风格的损失函数
    criterion_qnr = QNRLoss(device).to(device)  # 遥感专用QNR损失
    criterion_spectral = SpectralLoss().to(device)  # 光谱一致性损失
    criterion_spatial = SpatialLoss().to(device)  # 空间细节损失

    # 对抗损失 - 使用MSE损失(LSGAN风格)
    criterion_adv = nn.MSELoss().to(device)  # UCGAN使用LSGAN
    # 使用简单的L1损失作为循环一致性损失，更稳定
    criterion_cycle = nn.L1Loss().to(device)
    # 验证损失
    metrics = ImageMetrics(device)
    
# 加载完整数据集
    full_dataset = MedicalImageDataset(opt.MS_data_dir, opt.NTL_data_dir, opt.up_scale)

    # 计算训练集和验证集的大小
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * opt.val_ratio)
    train_size = dataset_size - val_size
    
    # 随机分割数据集
    train_dataset, val_dataset = random_split(
        full_dataset, 
        [train_size, val_size], 
        generator=torch.Generator().manual_seed(opt.seed)
    )
    
    logging.info(f"数据集总大小: {dataset_size}")
    logging.info(f"训练集大小: {train_size}")
    logging.info(f"验证集大小: {val_size}")
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=opt.batch_size, shuffle=True, num_workers=4, pin_memory=True)
    val_loader = DataLoader(val_dataset, batch_size=opt.batch_size, shuffle=False, num_workers=4, pin_memory=True)
    
    # 记录训练历史
    history = {
        'train_g_loss': [],
        'train_d_hr_loss': [],
        'train_d_lr_loss': [],
        'train_cycle_loss': [],
        'train_identity_loss': [],
        'val_g_loss': [],
        'val_d_hr_loss': [],
        'val_d_lr_loss': [],
        'val_cycle_loss': [],
        'val_identity_loss': [],
        'learning_rates_g': [],
        'learning_rates_d_hr': [],
        'learning_rates_d_lr': [],
        'val_psnr': [],      # PSNR指标
        'val_ssim': [],      # SSIM指标
        'val_perceptual': [], # 新增: 夜光影像感知质量指标
        'val_adv_score': [], # 新增: 对抗分数
        'val_mixed_score': [] # 改进的混合评分
}
    
    # 记录最佳模型和指标初始值
    best_val_g_loss = float('inf')
    best_val_psnr = 0.0  # PSNR越高越好，初始化为0
    best_mixed_score = 0.0

    # 滑动窗口保存策略
    mixed_score_history = []  # 存储混合评分历史
    window_size = 3  # 滑动窗口大小
    improvement_threshold = 0.01  # 改进阈值
    best_moving_average = 0.0  # 最佳滑动平均
    # 辅助函数：创建真实/虚假标签 - UCGAN风格软标签
    def create_labels(batch_size, real=True):
        if opt.use_soft_labels:
            if real:
                # UCGAN风格软标签：随机在[0.7, 1.2]范围
                label_value = 0.7 + np.random.rand() * 0.5
                return torch.ones(batch_size, 1, device=device) * label_value
            else:
                # 假标签：随机在[0, 0.3]范围
                label_value = 0.0 + np.random.rand() * 0.3
                return torch.ones(batch_size, 1, device=device) * label_value
        else:
            if real:
                return torch.ones(batch_size, 1, device=device) * 0.9  # 标签平滑
            else:
                return torch.zeros(batch_size, 1, device=device) + 0.1  # 标签平滑
# 开始训练
    for epoch in range(opt.epochs):
        # 训练阶段
        G_LR_to_HR.train()
        G_HR_to_LR.train()
        D_HR.train()
        D_LR.train()
        
        train_epoch_g_loss = 0
        train_epoch_d_hr_loss = 0
        train_epoch_d_lr_loss = 0
        train_epoch_cycle_loss = 0
        train_epoch_identity_loss = 0
        
        # 记录开始时间
        start_time = datetime.now()

        for i, ( lr_ntl,ms_img, hr_ntl) in enumerate(train_loader):
            ms_img, lr_ntl, hr_ntl = ms_img.to(device), lr_ntl.to(device), hr_ntl.to(device)
            batch_size = lr_ntl.size(0)
            
            #######################
            # (1) 更新判别器网络 #
            #######################
            if epoch >= opt.warmup_epochs:  # 预热期后才训练判别器
                
                # 更新D_HR（高分辨率判别器）- UCGAN风格优化
                optimizer_D_HR.zero_grad()

                # 真实HR图像
                d_hr_real = D_HR(hr_ntl)
                real_labels_hr = create_labels(batch_size, real=True)
                real_labels_hr = real_labels_hr.expand_as(d_hr_real)
                d_hr_real_loss = criterion_adv(d_hr_real, real_labels_hr)

                # 生成的HR图像
                fake_hr = G_LR_to_HR(lr_ntl, ms_img)
                d_hr_fake = D_HR(fake_hr.detach())
                fake_labels_hr = create_labels(batch_size, real=False)
                fake_labels_hr = fake_labels_hr.expand_as(d_hr_fake)
                d_hr_fake_loss = criterion_adv(d_hr_fake, fake_labels_hr)

                # D_HR总损失
                d_hr_loss = (d_hr_real_loss + d_hr_fake_loss) * 0.5
                d_hr_loss.backward()
                torch.nn.utils.clip_grad_norm_(D_HR.parameters(), max_norm=1.0)
                optimizer_D_HR.step()
                
                # 更新D_LR（低分辨率判别器）- UCGAN风格优化
                optimizer_D_LR.zero_grad()

                # 真实LR图像
                d_lr_real = D_LR(lr_ntl)
                real_labels_lr = create_labels(batch_size, real=True)
                real_labels_lr = real_labels_lr.expand_as(d_lr_real)
                d_lr_real_loss = criterion_adv(d_lr_real, real_labels_lr)

                # 生成的LR图像
                fake_lr = G_HR_to_LR(hr_ntl)
                d_lr_fake = D_LR(fake_lr.detach())
                fake_labels_lr = create_labels(batch_size, real=False)
                fake_labels_lr = fake_labels_lr.expand_as(d_lr_fake)
                d_lr_fake_loss = criterion_adv(d_lr_fake, fake_labels_lr)

                # D_LR总损失
                d_lr_loss = (d_lr_real_loss + d_lr_fake_loss) * 0.5
                d_lr_loss.backward()
                torch.nn.utils.clip_grad_norm_(D_LR.parameters(), max_norm=1.0)
                optimizer_D_LR.step()
                
                train_epoch_d_hr_loss += d_hr_loss.item()
                train_epoch_d_lr_loss += d_lr_loss.item()

            #######################
            # (2) 更新生成器网络 #
            #######################
            if i % opt.n_critic == 0:
                optimizer_G.zero_grad()
                
                # 前向传播：LR -> HR -> LR (完整循环)
                fake_hr = G_LR_to_HR(lr_ntl, ms_img)
                reconstructed_lr = G_HR_to_LR(fake_hr)
                # 前向传播：HR -> LR -> HR (反向循环)
                fake_lr = G_HR_to_LR(hr_ntl)
                reconstructed_hr = G_LR_to_HR(fake_lr, ms_img)
                
                # 1. QNR损失 - UCGAN风格遥感专用损失
                # 将多光谱图像作为"全色"图像的替代（取平均值）
                pan_substitute = torch.mean(ms_img, dim=1, keepdim=True)
                qnr_loss = criterion_qnr(pan_substitute, lr_ntl, fake_hr) * opt.lambda_qnr

                # 2. 光谱和空间损失 - UCGAN风格分离损失
                spectral_loss = criterion_spectral(fake_hr, hr_ntl) * opt.lambda_spectral
                spatial_loss = criterion_spatial(fake_hr, hr_ntl) * opt.lambda_spatial

                # 3. 对抗损失 - UCGAN风格
                if epoch >= opt.warmup_epochs:
                    # HR对抗损失
                    d_hr_fake_for_g = D_HR(fake_hr)
                    real_labels_hr = create_labels(batch_size, real=True)
                    real_labels_hr = real_labels_hr.expand_as(d_hr_fake_for_g)
                    adv_loss_hr = criterion_adv(d_hr_fake_for_g, real_labels_hr) * opt.lambda_adv

                    # LR对抗损失
                    d_lr_fake_for_g = D_LR(fake_lr)
                    real_labels_lr = create_labels(batch_size, real=True)
                    real_labels_lr = real_labels_lr.expand_as(d_lr_fake_for_g)
                    adv_loss_lr = criterion_adv(d_lr_fake_for_g, real_labels_lr) * opt.lambda_adv

                    adv_loss = adv_loss_hr + adv_loss_lr
                else:
                    adv_loss = torch.tensor(0.0, device=device)
                
                # 4. 循环一致性损失 - UCGAN风格优化
                cycle_loss_lr = criterion_cycle(reconstructed_lr, lr_ntl) * opt.lambda_cycle
                cycle_loss_hr = criterion_cycle(reconstructed_hr, hr_ntl) * opt.lambda_cycle
                cycle_loss = cycle_loss_lr + cycle_loss_hr
                
                # 5. 身份损失：简化版本，避免尺寸不匹配
                # 只对LR->HR生成器进行身份约束：HR下采样后再上采样应该接近原始HR
                hr_downsampled = F.interpolate(hr_ntl, scale_factor=1.0/opt.up_scale, mode='area')
                identity_hr = G_LR_to_HR(hr_downsampled, ms_img)
                identity_loss = criterion_cycle(identity_hr, hr_ntl) * opt.lambda_identity

                # 6. 改进的特征一致性损失：使用更合理的权重
                lr_upsampled = F.interpolate(lr_ntl, scale_factor=opt.up_scale, mode='bicubic', align_corners=False)
                feature_consistency_loss = criterion_cycle(fake_hr, lr_upsampled) * 0.5  # 增加权重

                # 7. 添加频域损失：确保高频细节保持
                def fft_loss(pred, target, weight=0.1):
                    pred_fft = torch.fft.fft2(pred)
                    target_fft = torch.fft.fft2(target)
                    return weight * F.l1_loss(torch.abs(pred_fft), torch.abs(target_fft))

                freq_loss = fft_loss(fake_hr, hr_ntl, 0.1)

                # 生成器总损失 - UCGAN风格：QNR+光谱+空间+对抗+循环+身份+特征一致性+频域
                g_loss = (qnr_loss + spectral_loss + spatial_loss + adv_loss +
                         cycle_loss + identity_loss + feature_consistency_loss + freq_loss)
                g_loss.backward()
                
                torch.nn.utils.clip_grad_norm_(G_LR_to_HR.parameters(), max_norm=1.0)
                torch.nn.utils.clip_grad_norm_(G_HR_to_LR.parameters(), max_norm=1.0)
                optimizer_G.step()
                
                train_epoch_g_loss += g_loss.item()
                train_epoch_cycle_loss += cycle_loss.item()
                train_epoch_identity_loss += identity_loss.item()
                
                if (i+1) % 10 == 0 or (i+1) == len(train_loader):
                    if epoch >= opt.warmup_epochs:
                        logging.info(f'Epoch [{epoch+1}/{opt.epochs}], Step [{i+1}/{len(train_loader)}], '
                                    f'G Loss: {g_loss.item():.4f}, D_HR Loss: {d_hr_loss.item():.4f}, '
                                    f'D_LR Loss: {d_lr_loss.item():.4f}, '
                                    f'QNR: {qnr_loss.item():.4f}, Spectral: {spectral_loss.item():.4f}, '
                                    f'Spatial: {spatial_loss.item():.4f}, Adv: {adv_loss.item():.4f}, '
                                    f'Cycle: {cycle_loss.item():.4f}')
                    else:
                        logging.info(f'Warmup Epoch [{epoch+1}/{opt.epochs}], Step [{i+1}/{len(train_loader)}], '
                                    f'G Loss: {g_loss.item():.4f}, '
                                    f'QNR: {qnr_loss.item():.4f}, Spectral: {spectral_loss.item():.4f}, '
                                    f'Spatial: {spatial_loss.item():.4f}, Cycle: {cycle_loss.item():.4f}')
# 计算平均训练损失
        avg_train_g_loss = train_epoch_g_loss / (len(train_loader) // opt.n_critic) if train_epoch_g_loss > 0 else 0
        avg_train_d_hr_loss = train_epoch_d_hr_loss / len(train_loader) if train_epoch_d_hr_loss > 0 else 0
        avg_train_d_lr_loss = train_epoch_d_lr_loss / len(train_loader) if train_epoch_d_lr_loss > 0 else 0
        avg_train_cycle_loss = train_epoch_cycle_loss / (len(train_loader) // opt.n_critic) if train_epoch_cycle_loss > 0 else 0
        avg_train_identity_loss = train_epoch_identity_loss / (len(train_loader) // opt.n_critic) if train_epoch_identity_loss > 0 else 0
        
        history['train_g_loss'].append(avg_train_g_loss)
        history['train_d_hr_loss'].append(avg_train_d_hr_loss)
        history['train_d_lr_loss'].append(avg_train_d_lr_loss)
        history['train_cycle_loss'].append(avg_train_cycle_loss)
        history['train_identity_loss'].append(avg_train_identity_loss)
        
        # 验证阶段
        G_LR_to_HR.eval()
        G_HR_to_LR.eval()
        D_HR.eval()
        D_LR.eval()
        
        val_epoch_g_loss = 0
        val_epoch_d_hr_loss = 0
        val_epoch_d_lr_loss = 0
        val_epoch_cycle_loss = 0
        val_epoch_identity_loss = 0

        with torch.no_grad():
            val_psnr_scores = []
            val_ssim_scores = []
            val_perceptual_scores = []
            val_adv_scores = []

            for i, ( lr_ntl,ms_img, hr_ntl) in enumerate(val_loader):
                ms_img, lr_ntl, hr_ntl = ms_img.to(device), lr_ntl.to(device), hr_ntl.to(device)
                batch_size = lr_ntl.size(0)


                # 前向传播
                fake_hr = G_LR_to_HR(lr_ntl, ms_img)
                fake_lr = G_HR_to_LR(hr_ntl)
                reconstructed_lr = G_HR_to_LR(fake_hr)
                reconstructed_hr = G_LR_to_HR(fake_lr, ms_img)

                # 计算PSNR、SSIM和夜光影像感知质量
                psnr_val, ssim_val, perceptual_val = metrics.calculate_metrics(fake_hr, hr_ntl)
                val_psnr_scores.append(psnr_val)
                val_ssim_scores.append(ssim_val)
                val_perceptual_scores.append(perceptual_val)

                # 计算对抗分数 (使用生成器输出通过判别器的置信度)
                # 使用sigmoid将判别器输出转换为概率
                d_hr_output = D_HR(fake_hr)
                adv_score = torch.sigmoid(d_hr_output).mean().item()
                val_adv_scores.append(adv_score)

                # 计算损失 - UCGAN风格
                # QNR损失
                pan_substitute = torch.mean(ms_img, dim=1, keepdim=True)
                qnr_loss = criterion_qnr(pan_substitute, lr_ntl, fake_hr) * opt.lambda_qnr

                # 光谱和空间损失
                spectral_loss = criterion_spectral(fake_hr, hr_ntl) * opt.lambda_spectral
                spatial_loss = criterion_spatial(fake_hr, hr_ntl) * opt.lambda_spatial

                # 判别器损失
                if epoch >= opt.warmup_epochs:
                    # D_HR损失
                    d_hr_real = D_HR(hr_ntl)
                    real_labels_hr = create_labels(batch_size, real=True)
                    real_labels_hr = real_labels_hr.expand_as(d_hr_real)
                    d_hr_fake = D_HR(fake_hr)
                    fake_labels_hr = create_labels(batch_size, real=False)
                    fake_labels_hr = fake_labels_hr.expand_as(d_hr_fake)
                    d_hr_loss = (criterion_adv(d_hr_real, real_labels_hr) +
                               criterion_adv(d_hr_fake, fake_labels_hr)) * 0.5

                    # D_LR损失
                    d_lr_real = D_LR(lr_ntl)
                    real_labels_lr = create_labels(batch_size, real=True)
                    real_labels_lr = real_labels_lr.expand_as(d_lr_real)
                    d_lr_fake = D_LR(fake_lr)
                    fake_labels_lr = create_labels(batch_size, real=False)
                    fake_labels_lr = fake_labels_lr.expand_as(d_lr_fake)
                    d_lr_loss = (criterion_adv(d_lr_real, real_labels_lr) +
                               criterion_adv(d_lr_fake, fake_labels_lr)) * 0.5

                    # 对抗损失
                    real_labels_hr_g = create_labels(batch_size, real=True)
                    real_labels_hr_g = real_labels_hr_g.expand_as(d_hr_fake)
                    real_labels_lr_g = create_labels(batch_size, real=True)
                    real_labels_lr_g = real_labels_lr_g.expand_as(d_lr_fake)
                    adv_loss_hr = criterion_adv(d_hr_fake, real_labels_hr_g) * opt.lambda_adv
                    adv_loss_lr = criterion_adv(d_lr_fake, real_labels_lr_g) * opt.lambda_adv
                    adv_loss = adv_loss_hr + adv_loss_lr
                else:
                    d_hr_loss = torch.tensor(0.0)
                    d_lr_loss = torch.tensor(0.0)
                    adv_loss = torch.tensor(0.0)
                    qnr_loss = torch.tensor(0.0)
                    spectral_loss = torch.tensor(0.0)
                    spatial_loss = torch.tensor(0.0)

                # 循环一致性损失 - 使用简单L1损失
                cycle_loss = (criterion_cycle(reconstructed_lr, lr_ntl) +
                            criterion_cycle(reconstructed_hr, hr_ntl)) * opt.lambda_cycle

                # 身份损失
                hr_downsampled = F.interpolate(hr_ntl, scale_factor=1.0/opt.up_scale, mode='area')
                identity_hr = G_LR_to_HR(hr_downsampled, ms_img)
                identity_loss = criterion_cycle(identity_hr, hr_ntl) * opt.lambda_identity

                # 生成器总损失 - UCGAN风格
                g_loss = qnr_loss + spectral_loss + spatial_loss + adv_loss + cycle_loss + identity_loss

                val_epoch_g_loss += g_loss.item()
                val_epoch_d_hr_loss += d_hr_loss.item()
                val_epoch_d_lr_loss += d_lr_loss.item()
                val_epoch_cycle_loss += cycle_loss.item()
                val_epoch_identity_loss += identity_loss.item()
        
        # 计算平均验证损失
        avg_val_g_loss = val_epoch_g_loss / len(val_loader)
        avg_val_d_hr_loss = val_epoch_d_hr_loss / len(val_loader)
        avg_val_d_lr_loss = val_epoch_d_lr_loss / len(val_loader)
        avg_val_cycle_loss = val_epoch_cycle_loss / len(val_loader)
        avg_val_identity_loss = val_epoch_identity_loss / len(val_loader)
        # 计算平均指标
        avg_val_psnr = np.mean(val_psnr_scores)
        avg_val_ssim = np.mean(val_ssim_scores)
        avg_val_perceptual = np.mean(val_perceptual_scores)
        avg_val_adv_score = np.mean(val_adv_scores)

        # 计算改进的混合评分
        mixed_score = compute_mixed_score(avg_val_psnr, avg_val_ssim, avg_val_perceptual, avg_val_adv_score)
        
        history['val_g_loss'].append(avg_val_g_loss)
        history['val_d_hr_loss'].append(avg_val_d_hr_loss)
        history['val_d_lr_loss'].append(avg_val_d_lr_loss)
        history['val_cycle_loss'].append(avg_val_cycle_loss)
        history['val_identity_loss'].append(avg_val_identity_loss)
        history['val_psnr'].append(avg_val_psnr)
        history['val_ssim'].append(avg_val_ssim)
        history['val_perceptual'].append(avg_val_perceptual)
        history['val_adv_score'].append(avg_val_adv_score)
        history['val_mixed_score'].append(mixed_score)
        # 更新学习率
        if scheduler_G:
            if isinstance(scheduler_G, ReduceLROnPlateau):
                scheduler_G.step(avg_val_g_loss)
            else:
                scheduler_G.step()
                
        if scheduler_D_HR and epoch >= opt.warmup_epochs:
            if isinstance(scheduler_D_HR, ReduceLROnPlateau):
                scheduler_D_HR.step(avg_val_d_hr_loss)
            else:
                scheduler_D_HR.step()
                
        if scheduler_D_LR and epoch >= opt.warmup_epochs:
            if isinstance(scheduler_D_LR, ReduceLROnPlateau):
                scheduler_D_LR.step(avg_val_d_lr_loss)
            else:
                scheduler_D_LR.step()
        
        # 记录当前学习率
        current_lr_g = optimizer_G.param_groups[0]['lr']
        current_lr_d_hr = optimizer_D_HR.param_groups[0]['lr']
        current_lr_d_lr = optimizer_D_LR.param_groups[0]['lr']
        history['learning_rates_g'].append(current_lr_g)
        history['learning_rates_d_hr'].append(current_lr_d_hr)
        history['learning_rates_d_lr'].append(current_lr_d_lr)
        
        # 计算训练时间
# 计算训练时间
        epoch_time = datetime.now() - start_time
        
        logging.info(f'Epoch [{epoch+1}/{opt.epochs}], '
                    f'G训练: {avg_train_g_loss:.4f}, G验证: {avg_val_g_loss:.4f}, '
                    f'D_HR训练: {avg_train_d_hr_loss:.4f}, D_HR验证: {avg_val_d_hr_loss:.4f}, '
                    f'D_LR训练: {avg_train_d_lr_loss:.4f}, D_LR验证: {avg_val_d_lr_loss:.4f}, '
                    f'循环损失: {avg_val_cycle_loss:.4f}, '
                    f'PSNR: {avg_val_psnr:.2f}dB, SSIM: {avg_val_ssim:.4f}, '
                    f'感知质量: {avg_val_perceptual:.4f}, 对抗分数: {avg_val_adv_score:.4f}, '
                    f'混合评分: {mixed_score:.4f}, '
                    f'学习率G: {current_lr_g:.2e}, 学习率D_HR: {current_lr_d_hr:.2e}, '
                    f'学习率D_LR: {current_lr_d_lr:.2e}, 时间: {epoch_time}')
        
        # 改进的模型保存策略：滑动窗口 + Δ阈值
        mixed_score_history.append(mixed_score)

        # 计算滑动平均
        if len(mixed_score_history) >= window_size:
            current_moving_average = sum(mixed_score_history[-window_size:]) / window_size

            # 仅当滑动平均提升≥阈值时才保存模型
            if current_moving_average > best_moving_average + improvement_threshold:
                best_moving_average = current_moving_average
                best_mixed_score = mixed_score  # 更新最佳单点分数

                best_path = os.path.join(opt.save_dir, 'best_model.pth')
                torch.save({
                    'epoch': epoch + 1,
                    'G_LR_to_HR_state_dict': G_LR_to_HR.state_dict(),
                    'G_HR_to_LR_state_dict': G_HR_to_LR.state_dict(),
                    'D_HR_state_dict': D_HR.state_dict(),
                    'D_LR_state_dict': D_LR.state_dict(),
                    'optimizer_G_state_dict': optimizer_G.state_dict(),
                    'optimizer_D_HR_state_dict': optimizer_D_HR.state_dict(),
                    'optimizer_D_LR_state_dict': optimizer_D_LR.state_dict(),
                    'best_mixed_score': best_mixed_score,
                    'best_moving_average': best_moving_average,
                    'mixed_score_history': mixed_score_history,
                    'training_args': opt,
                    'history': history
                }, best_path)

                logging.info(f'保存最佳模型到: {best_path} '
                           f'(滑动平均: {current_moving_average:.4f}, '
                           f'改进: {current_moving_average - (best_moving_average - improvement_threshold):.4f})')
        else:
            # 前几个epoch，使用简单的最佳分数保存
            if mixed_score > best_mixed_score:
                best_mixed_score = mixed_score
                best_path = os.path.join(opt.save_dir, 'best_model.pth')
                torch.save({
                    'epoch': epoch + 1,
                    'G_LR_to_HR_state_dict': G_LR_to_HR.state_dict(),
                    'G_HR_to_LR_state_dict': G_HR_to_LR.state_dict(),
                    'D_HR_state_dict': D_HR.state_dict(),
                    'D_LR_state_dict': D_LR.state_dict(),
                    'optimizer_G_state_dict': optimizer_G.state_dict(),
                    'optimizer_D_HR_state_dict': optimizer_D_HR.state_dict(),
                    'optimizer_D_LR_state_dict': optimizer_D_LR.state_dict(),
                    'best_mixed_score': best_mixed_score,
                    'mixed_score_history': mixed_score_history,
                    'training_args': opt,
                    'history': history
                }, best_path)
                logging.info(f'保存最佳模型到: {best_path} (初期阶段，混合评分: {mixed_score:.4f})')

    # 训练完成后保存最终模型
    final_model_path = os.path.join(opt.save_dir, f'final_model.pth')
    torch.save({
        'epoch': opt.epochs,
        'G_LR_to_HR_state_dict': G_LR_to_HR.state_dict(),
        'G_HR_to_LR_state_dict': G_HR_to_LR.state_dict(),
        'D_HR_state_dict': D_HR.state_dict(),
        'D_LR_state_dict': D_LR.state_dict(),
        'optimizer_G_state_dict': optimizer_G.state_dict(),
        'optimizer_D_HR_state_dict': optimizer_D_HR.state_dict(),
        'optimizer_D_LR_state_dict': optimizer_D_LR.state_dict(),
        'best_val_g_loss': best_val_g_loss,
        'training_args': opt,
        'history': history
    }, final_model_path)
    logging.info(f'训练完成！最终模型保存到: {final_model_path}')   
    # 绘制训练曲线
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    visualizer.plot_training_curves(history, current_time)

    return history

if __name__ == '__main__':
    train()